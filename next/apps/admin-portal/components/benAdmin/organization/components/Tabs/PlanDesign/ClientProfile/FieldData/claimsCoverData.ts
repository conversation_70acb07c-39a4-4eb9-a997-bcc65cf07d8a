import { OrganizationDetails } from 'apps/admin-portal/components/benAdmin/Models/interfaces';
import { TemplateFieldConfig } from 'apps/admin-portal/components/benAdmin/ReusableComponents/Models/types';

export const getClaimsCoverFields = (
  orgDetails: Partial<OrganizationDetails>
): Partial<TemplateFieldConfig>[] => {
  const planPdx = orgDetails?.plan?.plan_pdx;
  //   console.log('planPdx', planPdx);
  return [
    {
      label: 'Cover Retail Claims',
      name: 'retail_claims_covered_ind',
      value: planPdx?.retail_claims_covered_ind,
    },
    {
      label: 'Cover Mail Claims',
      name: 'mail_claims_covered_ind',
      value: planPdx?.mail_claims_covered_ind,
    },
    {
      label: 'Cover Paper Claims',
      name: 'paper_claims_covered_ind',
      value: planPdx?.paper_claims_covered_ind,
    },
    {
      label: 'Cover Out of Network',
      name: 'out_of_network_claims_covered_ind',
      value: planPdx?.out_of_network_claims_covered_ind,
    },
    {
      label: 'Cover Foreign Claims',
      name: 'foreign_claims_covered',
      value: planPdx?.foreign_claims_covered,
    },
    {
      label: 'Allow for Coordination of Benefits (COBS)',
      name: 'cob_allowed_ind',
      value: planPdx?.cob_allowed_ind,
    },
    {
      label: 'Medicaid Subrogation Claims',
      name: 'medicaid_subrogation_ind',
      value: planPdx?.medicaid_subrogation_ind,
    },
    {
      label: 'Paper Claims Covered - Pricing',
      name: 'paper_claims_pricing_ind',
      value: planPdx?.paper_claims_pricing_ind,
    },
  ];
};
