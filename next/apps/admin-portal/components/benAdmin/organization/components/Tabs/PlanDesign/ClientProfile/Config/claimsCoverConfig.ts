import { getPropertyPath } from 'apps/admin-portal/components/benAdmin/ReusableComponents/Components/Form/components/hooks/formHelpers';

import { BASE_PATHS } from './configs';

const CLAIMS_COVER_FIELDS = {
  retail_claims_covered_ind: 'retail_claims_covered_ind',
  mail_claims_covered_ind: 'mail_claims_covered_ind',
  paper_claims_covered_ind: 'paper_claims_covered_ind',
  out_of_network_claims_covered_ind: 'out_of_network_claims_covered_ind',
  foreign_claims_covered: 'foreign_claims_covered',
  cob_allowed_ind: 'cob_allowed_ind',
  medicaid_subrogation_ind: 'medicaid_subrogation_ind',
  paper_claims_pricing_ind: 'paper_claims_pricing_ind',
};

export interface ClaimsCoverConfig {
  retail_claims_covered_ind: string;
  mail_claims_covered_ind: string;
  paper_claims_covered_ind: string;
  out_of_network_claims_covered_ind: string;
  foreign_claims_covered: string;
  cob_allowed_ind: string;
  medicaid_subrogation_ind: string;
  paper_claims_pricing_ind: string;
}

export const claimsCoverConfig: ClaimsCoverConfig = {
  retail_claims_covered_ind: getPropertyPath(
    BASE_PATHS.PLAN_PDX,
    CLAIMS_COVER_FIELDS.retail_claims_covered_ind
  ),
  mail_claims_covered_ind: getPropertyPath(
    BASE_PATHS.PLAN_PDX,
    CLAIMS_COVER_FIELDS.mail_claims_covered_ind
  ),
  paper_claims_covered_ind: getPropertyPath(
    BASE_PATHS.PLAN_PDX,
    CLAIMS_COVER_FIELDS.paper_claims_covered_ind
  ),
  out_of_network_claims_covered_ind: getPropertyPath(
    BASE_PATHS.PLAN_PDX,
    CLAIMS_COVER_FIELDS.out_of_network_claims_covered_ind
  ),
  foreign_claims_covered: getPropertyPath(
    BASE_PATHS.PLAN_PDX,
    CLAIMS_COVER_FIELDS.foreign_claims_covered
  ),
  cob_allowed_ind: getPropertyPath(
    BASE_PATHS.PLAN_PDX,
    CLAIMS_COVER_FIELDS.cob_allowed_ind
  ),
  medicaid_subrogation_ind: getPropertyPath(
    BASE_PATHS.PLAN_PDX,
    CLAIMS_COVER_FIELDS.medicaid_subrogation_ind
  ),
  paper_claims_pricing_ind: getPropertyPath(
    BASE_PATHS.PLAN_PDX,
    CLAIMS_COVER_FIELDS.paper_claims_pricing_ind
  ),
};

export function getClaimsCoverConfigPath(
  field: keyof ClaimsCoverConfig
): string {
  return claimsCoverConfig[field];
}
