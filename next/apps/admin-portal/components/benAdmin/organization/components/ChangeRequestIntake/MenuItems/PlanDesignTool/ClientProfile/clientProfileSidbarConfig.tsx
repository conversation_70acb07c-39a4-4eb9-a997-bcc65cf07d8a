import { OrganizationDetails } from 'apps/admin-portal/components/benAdmin/Models/interfaces';
import { SidebarConfig } from 'apps/admin-portal/components/benAdmin/ReusableComponents/Models/types';
import { UseFormReturn } from 'react-hook-form';

import {
  CLAIMS_COVER_ITEM,
  CLIENT_INFORMATION_ITEM,
  CLIENT_PROFILE_MODE,
  ELIGIBILITY_ITEM,
  FSA_HRA_HSA_ITEM,
  IMPLEMENTATION_ITEM,
  IN_HOUSE_PHARMACY_ITEM,
  PHARMACY_BENEFITS_MANAGER_ITEM,
} from '../../../Navigation/navigationConstants';
import ClaimsCoverComponent from './ClaimsCover/claimsCoverComponent';
import ClientInformationComponent from './ClientInformation/clientInformationComponent';
import EligibilityComponent from './Eligibility/eligibilityComponent';
import FsaHraHsaComponent from './FsaHraHsa/fsaHraHsaComponent';
import ImplementationComponent from './Implementation/implementationComponent';
import InHousePharmacyComponent from './InHousePharmacy/inHousePharmacyComponent';
import PharmacyBenefitsManagerComponent from './PharmacyBenefitsManager/pharmacyBenefitsManagerComponent';
import ClientProfileReview from './Review/clientProfileReview';

export const getClientProfileSidebarConfig = (
  changeRequest: OrganizationDetails,
  formMethods: UseFormReturn<any>,
  navigateFn?: (section: string, tab: string) => void
): SidebarConfig => {
  const navigateToClientProfileItem = (id: string) =>
    navigateFn ? navigateFn(CLIENT_PROFILE_MODE, id) : undefined;

  return {
    sections: [
      {
        title: 'CLIENT PROFILE',
        items: [
          {
            id: CLIENT_INFORMATION_ITEM,
            label: 'Client Information',
            component: (
              <ClientInformationComponent
                formMethods={formMethods}
                onUpdateActiveItem={navigateToClientProfileItem}
              />
            ),
          },
          {
            id: PHARMACY_BENEFITS_MANAGER_ITEM,
            label: 'Pharmacy Benefits Manager',
            component: (
              <PharmacyBenefitsManagerComponent
                formMethods={formMethods}
                onUpdateActiveItem={navigateToClientProfileItem}
              />
            ),
          },
          {
            id: IN_HOUSE_PHARMACY_ITEM,
            label: 'In-House Pharmacy',
            component: (
              <InHousePharmacyComponent
                formMethods={formMethods}
                onUpdateActiveItem={navigateToClientProfileItem}
              />
            ),
          },
          {
            id: IMPLEMENTATION_ITEM,
            label: 'Implementation',
            component: (
              <ImplementationComponent
                formMethods={formMethods}
                onUpdateActiveItem={navigateToClientProfileItem}
              />
            ),
          },
          {
            id: ELIGIBILITY_ITEM,
            label: 'Eligibility',
            component: (
              <EligibilityComponent
                formMethods={formMethods}
                onUpdateActiveItem={navigateToClientProfileItem}
              />
            ),
          },
          {
            id: CLAIMS_COVER_ITEM,
            label: 'Claims Cover',
            component: (
              <ClaimsCoverComponent
                formMethods={formMethods}
                onUpdateActiveItem={navigateToClientProfileItem}
              />
            ),
          },
          {
            id: FSA_HRA_HSA_ITEM,
            label: 'FSA/HRA/HSA',
            component: (
              <FsaHraHsaComponent
                formMethods={formMethods}
                onUpdateActiveItem={navigateToClientProfileItem}
              />
            ),
          },
          {
            id: 'CLIENT_PROFILE_REVIEW',
            label: 'Review & Save',
            component: (
              <ClientProfileReview
                formMethods={formMethods}
                onUpdateActiveItem={navigateToClientProfileItem}
                currentDetails={changeRequest}
              />
            ),
          },
        ],
      },
    ],
  };
};
