import { OrganizationDetails } from 'apps/admin-portal/components/benAdmin/Models/interfaces';
import { usePicklistMaps } from 'apps/admin-portal/components/benAdmin/organization/maps/picklistMaps';
import {
  defineFormField,
  defineInlineFieldGroup,
  defineSubCategory,
} from 'apps/admin-portal/components/benAdmin/ReusableComponents/Components/Form/components/hooks/formHelpers';

import { claimsCoverConfig } from '../../../../../Tabs/PlanDesign/ClientProfile/Config/claimsCoverConfig';

/**
 * useClaimsCoverForm Hook
 *
 * Generates form subcategories (sections) for claims coverage configuration
 * based on the provided organization details.
 *
 * @param currentDetails - Partial organization data used to prefill form fields
 * @returns An object containing the generated subCategories for the form
 */
export function useClaimsCoverForm(
  currentDetails: Partial<OrganizationDetails>
) {
  const { yesNoMap } = usePicklistMaps();
  const planPdx = currentDetails?.plan?.plan_pdx;

  // Define form subcategories
  const subCategories = [
    defineSubCategory(
      'Claims Coverage Settings',
      'Configure which types of claims are covered for this organization.',
      [
        defineInlineFieldGroup([
          defineFormField(
            'Cover Retail Claims',
            'dropdownSelect',
            claimsCoverConfig.retail_claims_covered_ind,
            planPdx?.retail_claims_covered_ind,
            {
              optionsMap: yesNoMap,
              isRequired: true,
              infoText: 'Specify whether retail claims are covered',
            }
          ),
          defineFormField(
            'Cover Mail Claims',
            'dropdownSelect',
            claimsCoverConfig.mail_claims_covered_ind,
            planPdx?.mail_claims_covered_ind,
            {
              optionsMap: yesNoMap,
              isRequired: true,
              infoText: 'Specify whether mail claims are covered',
            }
          ),
        ]),
        defineInlineFieldGroup([
          defineFormField(
            'Cover Paper Claims',
            'dropdownSelect',
            claimsCoverConfig.paper_claims_covered_ind,
            planPdx?.paper_claims_covered_ind,
            {
              optionsMap: yesNoMap,
              isRequired: true,
              infoText: 'Specify whether paper claims are covered',
            }
          ),
          defineFormField(
            'Cover Out of Network',
            'dropdownSelect',
            claimsCoverConfig.out_of_network_claims_covered_ind,
            planPdx?.out_of_network_claims_covered_ind,
            {
              optionsMap: yesNoMap,
              isRequired: true,
              infoText: 'Specify whether out of network claims are covered',
            }
          ),
        ]),
      ]
    ),
  ];

  return {
    subCategories,
  };
}
