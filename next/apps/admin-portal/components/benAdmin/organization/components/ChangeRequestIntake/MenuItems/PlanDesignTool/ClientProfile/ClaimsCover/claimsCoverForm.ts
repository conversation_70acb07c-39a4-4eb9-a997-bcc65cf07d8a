import { OrganizationDetails } from 'apps/admin-portal/components/benAdmin/Models/interfaces';
import { usePicklistMaps } from 'apps/admin-portal/components/benAdmin/organization/maps/picklistMaps';
import {
  defineFormField,
  defineInlineFieldGroup,
  defineSubCategory,
} from 'apps/admin-portal/components/benAdmin/ReusableComponents/Components/Form/components/hooks/formHelpers';

import { claimsCoverConfig } from '../../../../../Tabs/PlanDesign/ClientProfile/Config/claimsCoverConfig';

/**
 * useClaimsCoverForm Hook
 *
 * Generates form subcategories (sections) for claims coverage configuration
 * based on the provided organization details.
 *
 * @param currentDetails - Partial organization data used to prefill form fields
 * @returns An object containing the generated subCategories for the form
 */
export function useClaimsCoverForm(
  currentDetails: Partial<OrganizationDetails>
) {
  const { yesNoMap } = usePicklistMaps();
  const planPdx = currentDetails?.plan?.plan_pdx;

  // Define form subcategories
  const subCategories = [
    defineSubCategory(
      'Claims Coverage Settings',
      'Configure which types of claims are covered for this organization.',
      [
        defineInlineFieldGroup([
          defineFormField(
            'Cover Retail Claims',
            'dropdownSelect',
            claimsCoverConfig.retail_claims_covered_ind,
            planPdx?.retail_claims_covered_ind,
            {
              optionsMap: yesNoMap,
              isRequired: true,
              infoText: 'Specify whether retail claims are covered',
            }
          ),
          defineFormField(
            'Cover Mail Claims',
            'dropdownSelect',
            claimsCoverConfig.mail_claims_covered_ind,
            planPdx?.mail_claims_covered_ind,
            {
              optionsMap: yesNoMap,
              isRequired: true,
              infoText: 'Specify whether mail claims are covered',
            }
          ),
        ]),
        defineInlineFieldGroup([
          defineFormField(
            'Cover Paper Claims',
            'dropdownSelect',
            claimsCoverConfig.paper_claims_covered_ind,
            planPdx?.paper_claims_covered_ind,
            {
              optionsMap: yesNoMap,
              isRequired: true,
              infoText: 'Specify whether paper claims are covered',
            }
          ),
          defineFormField(
            'Cover Out of Network',
            'dropdownSelect',
            claimsCoverConfig.out_of_network_claims_covered_ind,
            planPdx?.out_of_network_claims_covered_ind,
            {
              optionsMap: yesNoMap,
              isRequired: true,
              infoText: 'Specify whether out of network claims are covered',
            }
          ),
        ]),
        defineInlineFieldGroup([
          defineFormField(
            'Cover Foreign Claims',
            'dropdownSelect',
            claimsCoverConfig.foreign_claims_covered,
            planPdx?.foreign_claims_covered,
            {
              optionsMap: yesNoMap,
              isRequired: true,
              infoText: 'Specify whether foreign claims are covered',
            }
          ),
          defineFormField(
            'Allow for Coordination of Benefits (COB)',
            'dropdownSelect',
            claimsCoverConfig.cob_allowed_ind,
            planPdx?.cob_allowed_ind,
            {
              optionsMap: yesNoMap,
              isRequired: true,
              infoText: 'Specify whether coordination of benefits is allowed',
            }
          ),
        ]),
        defineInlineFieldGroup([
          defineFormField(
            'Medicaid Subrogation Claims',
            'dropdownSelect',
            claimsCoverConfig.medicaid_subrogation_ind,
            planPdx?.medicaid_subrogation_ind,
            {
              optionsMap: yesNoMap,
              isRequired: true,
              infoText:
                'Specify whether medicaid subrogation claims are covered',
            }
          ),
          defineFormField(
            'Paper Claims Covered - Pricing',
            'dropdownSelect',
            claimsCoverConfig.paper_claims_pricing_ind,
            planPdx?.paper_claims_pricing_ind,
            {
              optionsMap: yesNoMap,
              isRequired: true,
              infoText: 'Specify paper claims pricing coverage',
            }
          ),
        ]),
      ]
    ),
  ];

  return {
    subCategories,
  };
}
