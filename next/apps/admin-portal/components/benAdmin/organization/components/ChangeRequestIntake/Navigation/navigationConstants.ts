// Constants for main configurations
export const MAIN_MODE = 'main';

// Constants for client profile configurations
export const CLIENT_PROFILE_MODE = 'client-profile';

export const CLIENT_INFORMATION_ITEM = 'client-information';
export const PHARMACY_BENEFITS_MANAGER_ITEM = 'pharmacy-benefits-manager';
export const IN_HOUSE_PHARMACY_ITEM = 'in-house-pharmacy';
export const IMPLEMENTATION_ITEM = 'implementation';
export const ELIGIBILITY_ITEM = 'eligibility';
export const FSA_HRA_HSA_ITEM = 'fsa-hra-hsa';
export const CLAIMS_COVER_ITEM = 'claims-cover';

// Constants for member experience configurations
export const MEMBER_EXPERIENCE_MODE = 'member-experience';

export const ID_CARDS_ITEM = 'id-cards';
export const TRANSITION_FILES_AND_DETAILS_ITEM = 'transition-files-and-details';
export const WELCOME_KIT_AND_LETTERS_ITEM = 'welcome-kit-and-letters';
export const MEMBER_SERVICES_ITEM = 'member-services';
export const MEMBER_SERVICES_REVIEW_ITEM = 'member-services-review';

// Constants for products and services configurations
export const PRODUCTS_AND_SERVICES_MODE = 'products-and-services';

export const CORE_PRODUCTS_ITEM = 'core-products';
export const PRODUCT_SET_UP_ITEM = 'product-set-up';
export const PHARMACY_NETWORK_ITEM = 'pharmacy-network';
export const RXB_PRODUCTS_ITEM = 'rxb-products';
export const PBM_PRODUCTS_ITEM = 'pbm-products';
export const THIRD_PARTY_PRODUCTS_ITEM = 'third-party-products';
export const PRODUCTS_AND_SERVICES_REVIEW_ITEM = 'products-and-services-review';

// Constants for plan design configurations
export const PLAN_DESIGN_MODE = 'plan-design';
export const GENERAL_ITEM = 'general';
export const XML_ITEM = 'xml';
export const PHARMACY_ACCUMULATORS_ITEM = 'pharmacy-accumulators';
export const ACCUMULATORS_GENERAL_ITEM = 'pharmacy-accumulators-general';
export const DEDUCTIBLE_ITEM = 'deductible';
export const MAXIMUM_OUT_OF_POCKET_ITEM = 'maximum-out-of-pocket';
export const OTHER_CAP_ITEM = 'other-cap';
export const PATIENT_PAY_ITEM = 'patient-pay';
export const STANDARD_ITEM = 'standard';
export const UNBREAKABLE_ITEM = 'unbreakable';
export const COMPOUND_ITEM = 'compound';
export const DISPENSE_ITEM = 'dispense';
export const PLAN_DESIGN_LIST_ITEM = 'plan-design-list';
export const PLAN_DESIGN_REVIEW_ITEM = 'plan-design-review';

// Constants for clinical design configurations
export const CLINICAL_DESIGN_MODE = 'clinical-design';
export const FORMULARY_SETUP_ITEM = 'formulary-set-up';
export const FORMULARY_INCLUSION_ITEM = 'formulary-inclusion';
export const FORMULARY_EXCLUSION_ITEM = 'formulary-exclusion';
export const DRUG_CLASS_ITEM = 'drug-class';
export const CONDITION_SPECIFIC_ITEM = 'condition-specific';
export const REGULATORY_REQUIREMENT_ITEM = 'regulatory-requirement';
export const DIABETICS_COVERAGE_ITEM = 'diabetics-coverage';
export const UTILIZATION_MANAGEMENT_ITEM = 'utilization-management';
export const INDEPENDENT_REVIEW_ITEM = 'independent-review';
export const AFFORDABLE_CARE_ITEM = 'affordable-care';
export const PREVENTATIVE_LIST_AND_VACCINE_ITEM =
  'preventative-list-and-vaccine';
export const BUNDLE_ITEM = 'bundle';
export const PRIOR_AUTHORIZATION_ITEM = 'prior-authorization';
export const QUANTITY_LIMIT_ITEM = 'quantity-limit';
export const STEP_THERAPY_ITEM = 'step-therapy';
